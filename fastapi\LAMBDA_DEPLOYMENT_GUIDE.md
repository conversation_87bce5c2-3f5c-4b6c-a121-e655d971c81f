# AWS Lambda Deployment Guide for ReplyPal

This guide explains how to deploy the ReplyPal FastAPI application to AWS Lambda using the generated `replypal_lambda.zip` package.

## Prerequisites

1. AWS CLI configured with appropriate permissions
2. AWS Lambda service access
3. API Gateway service access (optional, for HTTP endpoints)
4. DynamoDB tables already created (replypal-user-subscriptions, replypal-usage-records)

## Deployment Steps

### 1. Create Lambda Function

#### Using AWS Console:
1. Go to AWS Lambda Console
2. Click "Create function"
3. Choose "Author from scratch"
4. Function name: `replypal-api`
5. Runtime: `Python 3.9` or `Python 3.10`
6. Architecture: `x86_64`
7. Click "Create function"

#### Using AWS CLI:
```bash
aws lambda create-function \
  --function-name replypal-api \
  --runtime python3.9 \
  --role arn:aws:iam::YOUR_ACCOUNT:role/lambda-execution-role \
  --handler lambda_handler.handler \
  --zip-file fileb://replypal_lambda.zip \
  --timeout 30 \
  --memory-size 512
```

### 2. Upload Deployment Package

#### Using AWS Console:
1. In the Lambda function page, go to "Code" tab
2. Click "Upload from" → ".zip file"
3. Select `replypal_lambda.zip`
4. Click "Save"

#### Using AWS CLI:
```bash
aws lambda update-function-code \
  --function-name replypal-api \
  --zip-file fileb://replypal_lambda.zip
```

### 3. Configure Lambda Function

#### Handler Configuration:
- Handler: `lambda_handler.handler`

#### Environment Variables:
Set the following environment variables in Lambda:

```
# Application settings
APP_NAME=ReplyPal API
APP_VERSION=1.0.0
ENVIRONMENT=production
LOG_LEVEL=INFO
CORS_ORIGINS=*
REQUEST_TIMEOUT=30
MAX_REQUEST_SIZE=10000

# AI Model settings
AI_PROVIDER=deepseek
MODEL_TEMPERATURE=0.7
MODEL_MAX_TOKENS=500

# OpenAI settings (if using OpenAI)
OPENAI_API_KEY=your-openai-api-key
OPENAI_API_MODEL=gpt-3.5-turbo

# DeepSeek settings (if using DeepSeek)
DEEPSEEK_API_KEY=your-deepseek-api-key
DEEPSEEK_API_BASE=https://api.deepseek.com/v1
DEEPSEEK_API_MODEL=deepseek-chat

# AWS settings
AWS_REGION=us-east-1

# DynamoDB settings
USE_MOCK_DYNAMODB=false
DYNAMODB_CUSTOMERS_TABLE=replypal-customers
DYNAMODB_SUBSCRIPTIONS_TABLE=replypal-subscriptions
DYNAMODB_USER_SUBSCRIPTIONS_TABLE=replypal-user-subscriptions
DYNAMODB_USAGE_RECORDS_TABLE=replypal-usage-records

# Stripe settings
STRIPE_PUBLISHABLE_KEY=your-stripe-publishable-key
STRIPE_SECRET_KEY=your-stripe-secret-key
STRIPE_WEBHOOK_SECRET=your-stripe-webhook-secret
STRIPE_BASIC_PRICE_ID=your-stripe-price-id

# Auto-renewal settings
AUTO_RENEWAL_TOKEN_THRESHOLD=1500
AUTO_RENEWAL_TIME_THRESHOLD_DAYS=30

# Frontend URLs
FRONTEND_SUCCESS_URL=https://your-domain.com/success
FRONTEND_CANCEL_URL=https://your-domain.com/cancel

# Google OAuth settings
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=https://your-api-domain.com/auth/google-callback
```

#### Timeout and Memory:
- Timeout: 30 seconds (or higher for AI model calls)
- Memory: 512 MB (minimum recommended)

### 4. IAM Role Permissions

Ensure your Lambda execution role has the following permissions:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Action": [
                "logs:CreateLogGroup",
                "logs:CreateLogStream",
                "logs:PutLogEvents"
            ],
            "Resource": "arn:aws:logs:*:*:*"
        },
        {
            "Effect": "Allow",
            "Action": [
                "dynamodb:GetItem",
                "dynamodb:PutItem",
                "dynamodb:UpdateItem",
                "dynamodb:DeleteItem",
                "dynamodb:Query",
                "dynamodb:Scan"
            ],
            "Resource": [
                "arn:aws:dynamodb:*:*:table/replypal-*"
            ]
        }
    ]
}
```

### 5. API Gateway Integration (Optional)

To expose HTTP endpoints:

1. Create a new API Gateway (REST API)
2. Create a resource with proxy integration
3. Set integration type to "Lambda Function"
4. Enable "Use Lambda Proxy integration"
5. Select your Lambda function
6. Deploy the API

### 6. Testing

Test the deployment:

```bash
# Test health endpoint
curl https://your-api-gateway-url/ping

# Test with authentication
curl -X POST https://your-api-gateway-url/generate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-jwt-token" \
  -d '{
    "selected_text": "Hello, how are you?",
    "user_intent": "Reply politely",
    "tone": "friendly",
    "purpose": "reply"
  }'
```

## Important Notes

1. **Cold Starts**: Lambda functions may have cold start delays. Consider using provisioned concurrency for production.

2. **Package Size**: The deployment package is ~18MB, which is well within Lambda limits.

3. **Dependencies**: All required dependencies are included in the package.

4. **Environment Variables**: Make sure to set all required environment variables, especially API keys.

5. **DynamoDB**: Ensure your DynamoDB tables exist and the Lambda has proper permissions.

6. **Monitoring**: Use CloudWatch logs to monitor function execution and debug issues.

## Troubleshooting

### Common Issues:

1. **Import Errors**: Ensure all dependencies are included in the package
2. **Permission Errors**: Check IAM role permissions for DynamoDB access
3. **Timeout Errors**: Increase Lambda timeout for AI model calls
4. **Memory Errors**: Increase Lambda memory allocation

### Logs:
Check CloudWatch logs for detailed error messages:
```bash
aws logs describe-log-groups --log-group-name-prefix /aws/lambda/replypal-api
```

## Updating the Function

To update the function code:

1. Rebuild the package: `python build_lambda_package.py`
2. Upload the new ZIP file using AWS Console or CLI
3. Test the updated function

## Security Considerations

1. Store sensitive data (API keys) in AWS Secrets Manager
2. Use environment variables for configuration
3. Enable AWS X-Ray for tracing (optional)
4. Set up proper VPC configuration if needed
